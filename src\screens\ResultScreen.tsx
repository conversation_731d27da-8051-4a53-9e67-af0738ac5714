import React from 'react';
import { View, Text, ScrollView, TouchableOpacity } from 'react-native';
import tw from 'twrnc';

interface ResultScreenProps {
  navigation: any;
  route: any;
}

const ResultScreen: React.FC<ResultScreenProps> = ({ navigation, route }) => {
  const {
    score,
    totalQuestions,
    percentage,
    passed,
    categoryName,
    isMockExam = false,
    answers = {},
    questions = [],
    userName = '',
    userEmail = ''
  } = route.params;

  const getScoreColor = () => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = () => {
    if (percentage >= 90) return 'bg-green-50 border-green-200';
    if (percentage >= 70) return 'bg-yellow-50 border-yellow-200';
    return 'bg-red-50 border-red-200';
  };


  const getEncouragement = () => {
    if (passed) {
      return isMockExam 
        ? '🎉 ยินดีด้วย! คุณผ่านข้อสอบเสมือนจริงแล้ว พร้อมสำหรับการสอบจริง!'
        : '🎉 ยินดีด้วย! คุณผ่านการทดสอบในหมวดนี้แล้ว!';
    } else {
      return isMockExam
        ? '💪 ยังไม่ผ่าน แต่อย่าท้อใจ! ลองศึกษาเพิ่มเติมและทำข้อสอบใหม่'
        : '💪 ยังไม่ผ่าน แต่อย่าท้อใจ! ลองศึกษาข้อสอบพร้อมคำตอบและทำใหม่';
    }
  };

  return (
    <ScrollView style={tw`flex-1 bg-gray-100`}>
      <View style={tw`p-6`}>
        {/* Result Header */}
        <View style={tw`relative bg-white rounded-xl p-6 mb-6 shadow-lg items-center`}>
          <Text style={tw`text-2xl font-bold text-gray-800 mb-2 text-center`}>
            ผลการสอบ
          </Text>
          <Text style={tw`text-lg text-gray-600 text-center mb-2`}>
            {categoryName}
          </Text>

          {/* User Info */}
          {userName && (
            <View style={tw` top-0 right-0 bg-blue-50 rounded-lg p-2 mb-4 bg-opacity-0 `}>
              <Text style={tw`text-center text-blue-800 font-medium text-xl`}>
                ผู้ทำข้อสอบ: {userName}
              </Text>
              {/* {userEmail && (
                <Text style={tw`text-center text-blue-600 text-sm mt-1`}>
                  📧 {userEmail}
                </Text>
              )} */}
            </View>
          )}
          
          {/* Score Circle */}
          <View style={tw`${getScoreBgColor()} rounded-full w-32 h-32 items-center justify-center border-4 mb-4`}>
            <Text style={tw`text-4xl font-bold ${getScoreColor()}`}>
              {percentage}%
            </Text>
          </View>

          {/* Pass/Fail Status */}
          <View style={tw`${passed ? 'bg-green-100' : 'bg-red-100'} rounded-lg p-4 w-full`}>
            <Text style={tw`text-center font-bold text-lg ${passed ? 'text-green-800' : 'text-red-800'}`}>
              {passed ? '✅ ผ่าน' : '❌ ไม่ผ่าน'}
            </Text>
          </View>
        </View>

        {/* Score Details */}
        <View style={tw`bg-white rounded-xl p-6 mb-6 shadow-lg`}>
          <Text style={tw`text-xl font-bold text-gray-800 mb-4`}>
            รายละเอียดคะแนน
          </Text>
          
          <View style={tw`flex-row justify-between items-center mb-3 p-3 bg-gray-50 rounded-lg`}>
            <Text style={tw`text-gray-700 font-medium`}>ตอบถูก</Text>
            <Text style={tw`text-green-600 font-bold text-lg`}>{score} ข้อ</Text>
          </View>
          
          <View style={tw`flex-row justify-between items-center mb-3 p-3 bg-gray-50 rounded-lg`}>
            <Text style={tw`text-gray-700 font-medium`}>ตอบผิด</Text>
            <Text style={tw`text-red-600 font-bold text-lg`}>{totalQuestions - score} ข้อ</Text>
          </View>
          
          <View style={tw`flex-row justify-between items-center mb-3 p-3 bg-gray-50 rounded-lg`}>
            <Text style={tw`text-gray-700 font-medium`}>จำนวนข้อทั้งหมด</Text>
            <Text style={tw`text-blue-600 font-bold text-lg`}>{totalQuestions} ข้อ</Text>
          </View>

          {isMockExam && (
            <View style={tw`flex-row justify-between items-center p-3 bg-blue-50 rounded-lg`}>
              <Text style={tw`text-blue-700 font-medium`}>เกณฑ์ผ่าน</Text>
              <Text style={tw`text-blue-800 font-bold text-lg`}>45 ข้อ (90%)</Text>
            </View>
          )}
        </View>

        {/* Encouragement */}
        <View style={tw`bg-white rounded-xl p-6 mb-6 shadow-lg`}>
          <Text style={tw`text-center text-gray-800 text-lg leading-6`}>
            {getEncouragement()}
          </Text>
        </View>

        {/* Performance Analysis */}
        <View style={tw`bg-white rounded-xl p-6 mb-6 shadow-lg`}>
          <Text style={tw`text-xl font-bold text-gray-800 mb-4`}>
            การวิเคราะห์ผลงาน
          </Text>
          
          {percentage >= 90 && (
            <Text style={tw`text-green-700 text-base leading-6`}>
              🌟 ยอดเยี่ยม! คุณมีความเข้าใจในเนื้อหาเป็นอย่างดี พร้อมสำหรับการสอบจริงแล้ว
            </Text>
          )}
          
          {percentage >= 70 && percentage < 90 && (
            <Text style={tw`text-yellow-700 text-base leading-6`}>
              👍 ดีมาก! คุณมีพื้นฐานที่ดี แต่ควรทบทวนเนื้อหาเพิ่มเติมเพื่อให้ได้คะแนนที่สูงขึ้น
            </Text>
          )}
          
          {percentage < 70 && (
            <Text style={tw`text-red-700 text-base leading-6`}>
              📚 ควรศึกษาเนื้อหาเพิ่มเติม แนะนำให้ดูข้อสอบพร้อมคำตอบก่อนทำการทดสอบใหม่
            </Text>
          )}
        </View>

        {/* Action Buttons */}
        <View style={tw`gap-3`}>
          <TouchableOpacity
            style={tw`bg-blue-500 rounded-xl p-4 shadow-lg`}
            onPress={() => {
              if (isMockExam) {
                navigation.navigate('MockExam', { resetExam: true });
              } else {
                // For category tests, we need to pass the category info to reset
                const { categoryId, categoryName: catName } = route.params;
                navigation.navigate('QuizTest', {
                  categoryId,
                  categoryName: catName || categoryName,
                  resetExam: true
                });
              }
            }}
          >
            <Text style={tw`text-white font-bold text-lg text-center`}>
              🔄 ทำข้อสอบใหม่
            </Text>
          </TouchableOpacity>

          {(totalQuestions - score) > 0 && (
            <TouchableOpacity
              style={tw`bg-red-500 rounded-xl p-4 shadow-lg`}
              onPress={() => navigation.navigate('WrongAnswers', {
                answers,
                questions,
                categoryName,
                isMockExam
              })}
            >
              <Text style={tw`text-white font-bold text-lg text-center`}>
                ดูข้อสอบที่ตอบผิด ({totalQuestions - score} ข้อ)
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={tw`bg-gray-500 rounded-xl p-4 shadow-lg`}
            onPress={() => navigation.navigate('Home')}
          >
            <Text style={tw`text-white font-bold text-lg text-center`}>
              🏠 กลับสู่หน้าหลัก
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

export default ResultScreen;
