import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, TextInput, Alert } from 'react-native';
import tw from 'twrnc';
import { getCategoryQuestions, Question } from '../../data';

interface QuizViewScreenProps {
  navigation: any;
  route: any;
}

const QuizViewScreen: React.FC<QuizViewScreenProps> = ({ navigation, route }) => {
  const { categoryId, categoryName } = route.params;
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [inputValue, setInputValue] = useState('1');

  useEffect(() => {
    const categoryQuestions = getCategoryQuestions(categoryId);
    setQuestions(categoryQuestions);
  }, [categoryId]);

  useEffect(() => {
    setInputValue((currentIndex + 1).toString());
  }, [currentIndex]);

  // Navigation functions
  const handleFirst = () => {
    setCurrentIndex(0);
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handleLast = () => {
    setCurrentIndex(questions.length - 1);
  };

  const handleInputChange = (text: string) => {
    setInputValue(text);
  };

  const handleKeyDown = () => {
    const questionNumber = parseInt(inputValue);
    if (questionNumber >= 1 && questionNumber <= questions.length) {
      setCurrentIndex(questionNumber - 1);
    } else {
      Alert.alert(
        'หมายเลขข้อไม่ถูกต้อง',
        `กรุณาใส่หมายเลขข้อระหว่าง 1 ถึง ${questions.length}`,
        [{ text: 'ตกลง' }]
      );
      setInputValue((currentIndex + 1).toString());
    }
  };



  if (questions.length === 0) {
    return (
      <View style={tw`flex-1 bg-gray-100 justify-center items-center p-6`}>
        <Text style={tw`text-2xl mb-4`}>📚</Text>
        <Text style={tw`text-gray-600 text-lg text-center`}>กำลังโหลดข้อสอบ...</Text>
        <Text style={tw`text-gray-500 text-sm text-center mt-2`}>กรุณารอสักครู่</Text>
      </View>
    );
  }

  const currentQuestion = questions[currentIndex];

  const renderOption = (option: any, index: number) => {
    const isCorrect = index === currentQuestion.correctAnswer;
    
    return (
      <View
        key={index}
        style={tw`mb-3 p-4 rounded-lg border-2 ${
          isCorrect ? 'bg-green-50 border-green-500' : 'bg-gray-50 border-gray-200'
        }`}
      >
        <View style={tw`flex-row items-start`}>
          <Text style={tw`mr-3 flex-shrink-0 w-6 h-6 rounded-full border-2 text-center text-sm font-semibold ${
            isCorrect ? 'border-green-500 bg-green-500 text-white' : 'border-gray-400 text-gray-400'
          }`}>
            {String.fromCharCode(65 + index)}
          </Text>
          <View style={tw`flex-1`}>
            {option.text && (
              <Text style={tw`text-base ${
                isCorrect ? 'text-green-800' : 'text-gray-800'
              }`}>
                {option.text}
              </Text>
            )}
            {option.image && (
              <Image
                source={{ uri: option.image }}
                style={tw`w-full h-32 rounded-lg mt-2`}
                resizeMode="contain"
              />
            )}
          </View>
          
            {isCorrect && (
            <Text style={tw`text-green-600 font-bold mt-1`}>✓</Text>
          )}
          
        </View>
      </View>
    );
  };

  return (
    <View style={tw`flex-1 bg-gray-100`}>
      {/* Header */}
      <View style={tw`bg-white p-4 shadow-sm`}>
        <Text style={tw`text-lg font-bold text-gray-800 mb-1`}>
          {categoryName}
        </Text>
        <Text style={tw`text-gray-600`}>
          ข้อ {currentIndex + 1} จาก {questions.length}
        </Text>
      </View>

      <ScrollView style={tw`flex-1 p-4`}>
          {/* Question */}
          <View style={tw`bg-white rounded-lg p-4 mb-4 shadow-sm`}>
            <Text style={tw`text-lg font-semibold text-blue-800 mb-3`}>
              {currentQuestion.numbers}
            </Text>

            {currentQuestion.question.map((item, index) => (
              <View key={index} style={tw`mb-3`}>
                {item.text && (
                  <Text style={tw`text-base text-gray-800 leading-6`}>
                    {item.text}
                  </Text>
                )}
                {item.image && (
                  <Image
                    source={{ uri: item.image }}
                    style={tw`w-full h-60 rounded-lg mt-2`}
                    resizeMode="contain"
                  />
                )}
              </View>
            ))}
          </View>

          {/* Options */}
          <View style={tw`bg-white rounded-lg p-4 shadow-sm`}>
            <Text style={tw`text-lg font-semibold text-gray-800 mb-4`}>
              ตัวเลือก:
            </Text>
            {currentQuestion.options.map((option, index) => renderOption(option, index))}
          </View>
        </ScrollView>

      {/* Enhanced Navigation */}
      <View style={tw`bg-gray-100 p-4 shadow-lg`}>
        <View style={tw`bg-white rounded-lg p-3 shadow-sm`}>
          <View style={tw`flex-row items-center justify-center gap-4`}>
            {/* First Button */}
            <TouchableOpacity
              style={tw`py-2 px-3 rounded-md  ${
                currentIndex === 0 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleFirst}
              disabled={currentIndex === 0}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === 0 ? 'text-gray-500' : 'text-white'
              }`}>
                ‹‹
              </Text>
            </TouchableOpacity>

            {/* Previous Button */}
            <TouchableOpacity
              style={tw`py-2 px-4 rounded-md ${
                currentIndex === 0 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handlePrevious}
              disabled={currentIndex === 0}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === 0 ? 'text-gray-500' : 'text-white'
              }`}>
                ‹
              </Text>
            </TouchableOpacity>

            {/* Question Number Input */}
            <View style={tw`flex-row items-center mx-2`}>
              <TextInput
                style={tw`p-3 text-center border-2 border-blue-500 w-20 rounded-md bg-white`}
                value={inputValue}
                onChangeText={handleInputChange}
                onSubmitEditing={handleKeyDown}
                selectTextOnFocus={true}
                keyboardType="numeric"
                returnKeyType="go"
              />
            </View>

            {/* Next Button */}
            <TouchableOpacity
              style={tw`py-2 px-4 rounded-md ${
                currentIndex === questions.length - 1 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleNext}
              disabled={currentIndex === questions.length - 1}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === questions.length - 1 ? 'text-gray-500' : 'text-white'
              }`}>
                ›
              </Text>
            </TouchableOpacity>

            {/* Last Button */}
            <TouchableOpacity
              style={tw`py-2 px-3 rounded-md ${
                currentIndex === questions.length - 1 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleLast}
              disabled={currentIndex === questions.length - 1}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === questions.length - 1 ? 'text-gray-500' : 'text-white'
              }`}>
                ››
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};

export default QuizViewScreen;
