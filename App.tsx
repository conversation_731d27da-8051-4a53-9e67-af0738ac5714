import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import 'react-native-gesture-handler';

// Import screens
import HomeScreen from './src/screens/HomeScreen';
import CategoryScreen from './src/screens/CategoryScreen';
import QuizViewScreen from './src/screens/QuizViewScreen';
import QuizTestScreen from './src/screens/QuizTestScreen';
import MockExamScreen from './src/screens/MockExamScreen';
import ResultScreen from './src/screens/ResultScreen';
import WrongAnswersScreen from './src/screens/WrongAnswersScreen';
import AdminScreen from './src/screens/AdminScreen';

const Stack = createStackNavigator();

export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="light" backgroundColor="#1f2937" />
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#1f2937',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{ title: 'ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์' }}
        />
        <Stack.Screen
          name="Category"
          component={CategoryScreen}
          options={{ title: 'เลือกหมวดหมู่' }}
        />
        <Stack.Screen
          name="QuizView"
          component={QuizViewScreen}
          options={{ title: 'ข้อสอบพร้อมคำตอบ' }}
        />
        <Stack.Screen
          name="QuizTest"
          component={QuizTestScreen}
          options={{ title: 'ทดสอบข้อสอบ' }}
        />
        <Stack.Screen
          name="MockExam"
          component={MockExamScreen}
          options={{ title: 'ข้อสอบเสมือนจริง' }}
        />
        <Stack.Screen
          name="Result"
          component={ResultScreen}
          options={{ title: 'ผลการสอบ' }}
        />
        <Stack.Screen
          name="WrongAnswers"
          component={WrongAnswersScreen}
          options={{ title: 'ข้อสอบที่ตอบผิด' }}
        />
        <Stack.Screen
          name="Admin"
          component={AdminScreen}
          options={{ title: '📊 สถิติระบบ - แดชบอร์ด' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
