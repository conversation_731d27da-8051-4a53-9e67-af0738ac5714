import React, { useEffect, useRef } from 'react';
import { View, Animated, Easing } from 'react-native';
import tw from 'twrnc';

interface LoadingSpinnerProps {
  size?: number;
  color?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 48, 
  color = '#2563eb' 
}) => {
  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const spin = () => {
      spinValue.setValue(0);
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => spin());
    };
    spin();
  }, [spinValue]);

  const rotate = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        {
          width: size,
          height: size,
          borderWidth: 4,
          borderColor: '#e5e7eb',
          borderTopColor: color,
          borderRadius: size / 2,
          transform: [{ rotate }],
        },
      ]}
    />
  );
};

export default LoadingSpinner;
