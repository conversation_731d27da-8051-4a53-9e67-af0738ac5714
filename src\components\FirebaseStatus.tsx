import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import { database } from '../config/firebase';
import { ref, onValue } from 'firebase/database';
import tw from 'twrnc';

const FirebaseStatus: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);

  useEffect(() => {
    const connectedRef = ref(database, '.info/connected');
    const unsubscribe = onValue(connectedRef, (snapshot) => {
      setIsConnected(snapshot.val() === true);
    });

    return () => unsubscribe();
  }, []);

  if (isConnected === null) {
    return (
      <View style={tw`flex-row items-center`}>
        <View style={tw`w-2 h-2 bg-yellow-500 rounded-full mr-2`} />
        <Text style={tw`text-sm text-yellow-600`}>กำลังเชื่อมต่อ Firebase...</Text>
      </View>
    );
  }

  return (
    <View style={tw`flex-row items-center`}>
      <View style={tw`w-2 h-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'} rounded-full mr-2`} />
      <Text style={tw`text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
        Firebase: {isConnected ? 'เชื่อมต่อแล้ว' : 'ไม่ได้เชื่อมต่อ'}
      </Text>
    </View>
  );
};

export default FirebaseStatus;
