import { useState, useEffect } from 'react';
import { database } from '../config/firebase';
import { ref, onValue, push, set, get } from 'firebase/database';
import { storage } from '../utils/storage';

export interface PageView {
  page: string;
  views: number;
}

export interface CategoryStat {
  name: string;
  testTakers: number;
  studyViews: number;
}

export interface MockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number;
  timestamp: number;
}

export interface Statistics {
  totalPageViews: number;
  totalTestTakers: number;
  totalStudyViews: number;
  mockExamTestTakers: number;
  pageViews: PageView[];
  categoryStats: CategoryStat[];
  mockExamLeaderboard: MockExamResult[];
  mostPopularPage: string;
  mostPopularCategory: string;
}

const defaultStatistics: Statistics = {
  totalPageViews: 0,
  totalTestTakers: 0,
  totalStudyViews: 0,
  mockExamTestTakers: 0,
  pageViews: [],
  categoryStats: [],
  mockExamLeaderboard: [],
  mostPopularPage: 'ไม่มีข้อมูล',
  mostPopularCategory: 'ไม่มีข้อมูล'
};

export const useStatistics = () => {
  const [statistics, setStatistics] = useState<Statistics>(defaultStatistics);
  const [isLoading, setIsLoading] = useState(true);

  const loadStatistics = async () => {
    try {
      setIsLoading(true);
      
      // Try to load from Firebase first
      const statsRef = ref(database, 'statistics');
      const snapshot = await get(statsRef);
      
      if (snapshot.exists()) {
        const data = snapshot.val();
        setStatistics({
          ...defaultStatistics,
          ...data,
          pageViews: data.pageViews || [],
          categoryStats: data.categoryStats || [],
          mockExamLeaderboard: data.mockExamLeaderboard || []
        });
      } else {
        // If no Firebase data, try local storage as fallback
        const localData = await storage.getItem('statistics');
        if (localData) {
          setStatistics(JSON.parse(localData));
        } else {
          // Initialize with sample data
          await initializeSampleData();
        }
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
      // Fallback to local storage
      try {
        const localData = await storage.getItem('statistics');
        if (localData) {
          setStatistics(JSON.parse(localData));
        } else {
          await initializeSampleData();
        }
      } catch (localError) {
        console.error('Error loading local statistics:', localError);
        await initializeSampleData();
      }
    } finally {
      setIsLoading(false);
    }
  };

  const initializeSampleData = async () => {
    const sampleData: Statistics = {
      totalPageViews: 15420,
      totalTestTakers: 3250,
      totalStudyViews: 8750,
      mockExamTestTakers: 1200,
      pageViews: [
        { page: 'หน้าหลัก', views: 5420 },
        { page: 'ข้อสอบพร้อมคำตอบ', views: 3250 },
        { page: 'ทดสอบข้อสอบ', views: 2890 },
        { page: 'ข้อสอบเสมือนจริง', views: 2100 },
        { page: 'กฎจราจร', views: 1760 }
      ],
      categoryStats: [
        { name: 'กฎจราจร', testTakers: 850, studyViews: 2100 },
        { name: 'เครื่องหมายจราจร', testTakers: 720, studyViews: 1890 },
        { name: 'การขับขี่ปลอดภัย', testTakers: 680, studyViews: 1650 },
        { name: 'การบำรุงรักษา', testTakers: 520, studyViews: 1420 },
        { name: 'การคาดการณ์อุบัติเหตุ', testTakers: 480, studyViews: 1690 }
      ],
      mockExamLeaderboard: [
        {
          id: '1',
          userName: 'สมชาย ใจดี',
          userEmail: '<EMAIL>',
          score: 50,
          duration: 1200,
          timestamp: Date.now() - 86400000
        },
        {
          id: '2',
          userName: 'สมหญิง รักเรียน',
          userEmail: '<EMAIL>',
          score: 49,
          duration: 1350,
          timestamp: Date.now() - 172800000
        },
        {
          id: '3',
          userName: 'วิชัย มั่นใจ',
          userEmail: '<EMAIL>',
          score: 48,
          duration: 1180,
          timestamp: Date.now() - 259200000
        }
      ],
      mostPopularPage: 'หน้าหลัก',
      mostPopularCategory: 'กฎจราจร'
    };

    setStatistics(sampleData);
    
    // Save to both Firebase and AsyncStorage
    try {
      const statsRef = ref(database, 'statistics');
      await set(statsRef, sampleData);
    } catch (error) {
      console.error('Error saving to Firebase:', error);
    }

    try {
      await storage.setItem('statistics', JSON.stringify(sampleData));
    } catch (error) {
      console.error('Error saving to local storage:', error);
    }
  };

  const refreshStatistics = async () => {
    await loadStatistics();
  };

  const trackPageView = async (pageName: string) => {
    try {
      const pageViewRef = ref(database, `pageViews/${Date.now()}`);
      await set(pageViewRef, {
        page: pageName,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  };

  const trackTestTaker = async (category: string, mode: 'test' | 'study') => {
    try {
      const testRef = ref(database, `testTakers/${Date.now()}`);
      await set(testRef, {
        category,
        mode,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error tracking test taker:', error);
    }
  };

  const saveMockExamResult = async (result: Omit<MockExamResult, 'id'>) => {
    try {
      const resultsRef = ref(database, 'mockExamResults');
      await push(resultsRef, {
        ...result,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error saving mock exam result:', error);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  return {
    statistics,
    isLoading,
    refreshStatistics,
    trackPageView,
    trackTestTaker,
    saveMockExamResult
  };
};
