import { useState, useEffect } from 'react';
import { database } from '../config/firebase';
import { ref, onValue, push, set, get, remove } from 'firebase/database';
import { storage } from '../utils/storage';
import { categories } from '../../data/index';

export interface PageView {
  page: string;
  views: number;
}

export interface CategoryStat {
  name: string;
  testTakers: number;
  studyViews: number;
}

export interface MockExamResult {
  id?: string;
  userName: string;
  userEmail: string;
  score: number;
  duration: number;
  timestamp: number;
}

export interface Statistics {
  totalPageViews: number;
  totalTestTakers: number;
  totalStudyViews: number;
  mockExamTestTakers: number;
  pageViews: PageView[];
  categoryStats: CategoryStat[];
  mockExamLeaderboard: MockExamResult[];
  mostPopularPage: string;
  mostPopularCategory: string;
}

const defaultStatistics: Statistics = {
  totalPageViews: 0,
  totalTestTakers: 0,
  totalStudyViews: 0,
  mockExamTestTakers: 0,
  pageViews: [],
  categoryStats: [],
  mockExamLeaderboard: [],
  mostPopularPage: 'ไม่มีข้อมูล',
  mostPopularCategory: 'ไม่มีข้อมูล'
};

export const useStatistics = () => {
  const [statistics, setStatistics] = useState<Statistics>(defaultStatistics);
  const [isLoading, setIsLoading] = useState(true);

  const calculateStatisticsFromRawData = async () => {
    try {
      // Get raw data from Firebase
      const pageViewsRef = ref(database, 'pageViews');
      const testTakersRef = ref(database, 'testTakers');
      const mockExamResultsRef = ref(database, 'mockExamResults');

      const [pageViewsSnapshot, testTakersSnapshot, mockExamResultsSnapshot] = await Promise.all([
        get(pageViewsRef),
        get(testTakersRef),
        get(mockExamResultsRef)
      ]);

      // Process page views
      const pageViewsData = pageViewsSnapshot.exists() ? pageViewsSnapshot.val() : {};
      const pageViewCounts: { [key: string]: number } = {};
      let totalPageViews = 0;

      Object.values(pageViewsData).forEach((view: any) => {
        if (view.page) {
          pageViewCounts[view.page] = (pageViewCounts[view.page] || 0) + 1;
          totalPageViews++;
        }
      });

      const pageViews = Object.entries(pageViewCounts)
        .map(([page, views]) => ({ page, views }))
        .sort((a, b) => b.views - a.views);

      // Process test takers
      const testTakersData = testTakersSnapshot.exists() ? testTakersSnapshot.val() : {};
      const categoryTestCounts: { [key: string]: { testTakers: number; studyViews: number } } = {};
      let totalTestTakers = 0;
      let totalStudyViews = 0;

      // Initialize categories
      categories.forEach(category => {
        categoryTestCounts[category.name] = { testTakers: 0, studyViews: 0 };
      });

      Object.values(testTakersData).forEach((test: any) => {
        if (test.category) {
          if (!categoryTestCounts[test.category]) {
            categoryTestCounts[test.category] = { testTakers: 0, studyViews: 0 };
          }

          if (test.mode === 'test') {
            categoryTestCounts[test.category].testTakers++;
            totalTestTakers++;
          } else if (test.mode === 'study') {
            categoryTestCounts[test.category].studyViews++;
            totalStudyViews++;
          }
        }
      });

      const categoryStats = Object.entries(categoryTestCounts)
        .map(([name, stats]) => ({ name, ...stats }))
        .sort((a, b) => (b.testTakers + b.studyViews) - (a.testTakers + a.studyViews));

      // Process mock exam results
      const mockExamResultsData = mockExamResultsSnapshot.exists() ? mockExamResultsSnapshot.val() : {};
      const mockExamLeaderboard = Object.entries(mockExamResultsData)
        .map(([id, result]: [string, any]) => ({ id, ...result }))
        .sort((a, b) => {
          if (b.score !== a.score) return b.score - a.score;
          return a.duration - b.duration; // Lower duration is better for same score
        })
        .slice(0, 10); // Top 10

      const mockExamTestTakers = mockExamLeaderboard.length;

      // Calculate most popular
      const mostPopularPage = pageViews.length > 0 ? pageViews[0].page : 'ไม่มีข้อมูล';
      const mostPopularCategory = categoryStats.length > 0 ? categoryStats[0].name : 'ไม่มีข้อมูล';

      return {
        totalPageViews,
        totalTestTakers,
        totalStudyViews,
        mockExamTestTakers,
        pageViews,
        categoryStats,
        mockExamLeaderboard,
        mostPopularPage,
        mostPopularCategory
      };
    } catch (error) {
      console.error('Error calculating statistics:', error);
      return null;
    }
  };

  const loadStatistics = async () => {
    try {
      setIsLoading(true);

      // Calculate statistics from raw data
      const calculatedStats = await calculateStatisticsFromRawData();

      if (calculatedStats) {
        setStatistics(calculatedStats);

        // Save calculated statistics to Firebase and local storage
        try {
          const statsRef = ref(database, 'statistics');
          await set(statsRef, calculatedStats);
          await storage.setItem('statistics', JSON.stringify(calculatedStats));
        } catch (saveError) {
          console.error('Error saving calculated statistics:', saveError);
        }
      } else {
        // Fallback to stored statistics or initialize empty
        const statsRef = ref(database, 'statistics');
        const snapshot = await get(statsRef);

        if (snapshot.exists()) {
          const data = snapshot.val();
          setStatistics({
            ...defaultStatistics,
            ...data,
            pageViews: data.pageViews || [],
            categoryStats: data.categoryStats || [],
            mockExamLeaderboard: data.mockExamLeaderboard || []
          });
        } else {
          await initializeEmptyData();
        }
      }
    } catch (error) {
      console.error('Error loading statistics:', error);
      // Final fallback
      try {
        const localData = await storage.getItem('statistics');
        if (localData) {
          setStatistics(JSON.parse(localData));
        } else {
          await initializeEmptyData();
        }
      } catch (localError) {
        console.error('Error loading local statistics:', localError);
        await initializeEmptyData();
      }
    } finally {
      setIsLoading(false);
    }
  };

  const initializeEmptyData = async () => {
    const emptyData: Statistics = {
      totalPageViews: 0,
      totalTestTakers: 0,
      totalStudyViews: 0,
      mockExamTestTakers: 0,
      pageViews: [],
      categoryStats: categories.map((category) => ({
        name: category.name,
        testTakers: 0,
        studyViews: 0
      })),
      mockExamLeaderboard: [],
      mostPopularPage: 'ไม่มีข้อมูล',
      mostPopularCategory: 'ไม่มีข้อมูล'
    };

    setStatistics(emptyData);

    // Save to both Firebase and Local Storage
    try {
      const statsRef = ref(database, 'statistics');
      await set(statsRef, emptyData);
    } catch (error) {
      console.error('Error saving to Firebase:', error);
    }

    try {
      await storage.setItem('statistics', JSON.stringify(emptyData));
    } catch (error) {
      console.error('Error saving to local storage:', error);
    }
  };

  const refreshStatistics = async () => {
    // Force recalculation from raw data
    const calculatedStats = await calculateStatisticsFromRawData();
    if (calculatedStats) {
      setStatistics(calculatedStats);

      // Save updated statistics
      try {
        const statsRef = ref(database, 'statistics');
        await set(statsRef, calculatedStats);
        await storage.setItem('statistics', JSON.stringify(calculatedStats));
      } catch (error) {
        console.error('Error saving refreshed statistics:', error);
      }
    }
  };

  const trackPageView = async (pageName: string) => {
    try {
      const pageViewRef = ref(database, `pageViews/${Date.now()}`);
      await set(pageViewRef, {
        page: pageName,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  };

  const trackTestTaker = async (category: string, mode: 'test' | 'study') => {
    try {
      const testRef = ref(database, `testTakers/${Date.now()}`);
      await set(testRef, {
        category,
        mode,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error tracking test taker:', error);
    }
  };

  const saveMockExamResult = async (result: Omit<MockExamResult, 'id'>) => {
    try {
      const resultsRef = ref(database, 'mockExamResults');
      await push(resultsRef, {
        ...result,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error saving mock exam result:', error);
    }
  };

  const clearAllData = async () => {
    try {
      // Clear Firebase data
      const statisticsRef = ref(database, 'statistics');
      const pageViewsRef = ref(database, 'pageViews');
      const testTakersRef = ref(database, 'testTakers');
      const mockExamResultsRef = ref(database, 'mockExamResults');

      await Promise.all([
        remove(statisticsRef),
        remove(pageViewsRef),
        remove(testTakersRef),
        remove(mockExamResultsRef)
      ]);

      // Clear local storage
      await storage.removeItem('statistics');

      console.log('All data cleared successfully');

      // Reinitialize with empty data
      await initializeEmptyData();

    } catch (error) {
      console.error('Error clearing data:', error);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  return {
    statistics,
    isLoading,
    refreshStatistics,
    trackPageView,
    trackTestTaker,
    saveMockExamResult,
    clearAllData
  };
};
