// Import all quiz data from different categories
import { quizData as accidentPredictionData } from './accidentprediction';
import { quizData as maintenanceData } from './maintenancelasted';
import { quizData as safeDrivingData } from './safedriving';
import { quizData as trafficLawsData } from './traffic-laws';
import { quizData as trafficControlDevicesData } from './trafficcontroldevices';

// Re-export types
export type { Option, Question } from './accidentprediction';

// Category definitions
export interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  count: number;
}

export const categories: Category[] = [
  {
    id: 'accident-prediction',
    name: 'การคาดการณ์อุบัติเหตุ',
    description: 'ข้อสอบเกี่ยวกับการคาดการณ์และป้องกันอุบัติเหตุ',
    icon: '🚑',
    count: accidentPredictionData.length
  },
  {
    id: 'maintenance',
    name: 'การดูแลรักษายานยนต์',
    description: 'ข้อสอบเกี่ยวกับการดูแลและบำรุงรักษายานพาหนะ',
    icon: '🔧',
    count: maintenanceData.length
  },
  {
    id: 'safe-driving',
    name: 'การขับขี่อย่างปลอดภัย',
    description: 'ข้อสอบเกี่ยวกับหลักการขับขี่อย่างปลอดภัย',
    icon: '🛡️',
    count: safeDrivingData.length
  },
  {
    id: 'traffic-laws',
    name: 'กฎหมายจราจร',
    description: 'ข้อสอบเกี่ยวกับกฎหมายและระเบียบการจราจร',
    icon: '📋',
    count: trafficLawsData.length
  },
  {
    id: 'traffic-control-devices',
    name: 'ป้ายและเครื่องหมายจราจร',
    description: 'ข้อสอบเกี่ยวกับป้าย สัญญาณ และเครื่องหมายจราจร',
    icon: '🚦',
    count: trafficControlDevicesData.length
  }
];

// Function to get questions by category
export const getCategoryQuestions = (categoryId: string) => {
  switch (categoryId) {
    case 'accident-prediction':
      return accidentPredictionData;
    case 'maintenance':
      return maintenanceData;
    case 'safe-driving':
      return safeDrivingData;
    case 'traffic-laws':
      return trafficLawsData;
    case 'traffic-control-devices':
      return trafficControlDevicesData;
    default:
      return [];
  }
};

// Mock exam configuration
export const mockExamConfig = {
  totalQuestions: 50,
  timeLimit: 50, // 50 minutes
  questionCounts: {
    'accident-prediction': 6,
    'maintenance': 8,
    'safe-driving': 20,
    'traffic-laws': 6,
    'traffic-control-devices': 10
  },
  passingScore: 45 // 90% of 50 questions
};

// Function to generate mock exam using configured counts per category (total 50)
export const generateMockExam = () => {
  const allCategories = [
    { id: 'accident-prediction', data: accidentPredictionData },
    { id: 'maintenance', data: maintenanceData },
    { id: 'safe-driving', data: safeDrivingData },
    { id: 'traffic-laws', data: trafficLawsData },
    { id: 'traffic-control-devices', data: trafficControlDevicesData }
  ];

  const mockQuestions: any[] = [];

  allCategories.forEach(category => {
    const need = mockExamConfig.questionCounts[category.id as keyof typeof mockExamConfig.questionCounts] || 0;
    if (need <= 0) return;
    const shuffled = [...category.data].sort(() => 0.5 - Math.random());
    const selectedQuestions = shuffled.slice(0, need);

    // Renumber questions sequentially later; first collect
    selectedQuestions.forEach(question => {
      mockQuestions.push({ ...question });
    });
  });

  // Shuffle all questions to mix categories, then renumber sequentially
  const mixed = mockQuestions.sort(() => 0.5 - Math.random());
  return mixed.map((q, idx) => ({
    ...q,
    numbers: `ข้อที่ ${idx + 1} :`
  }));
};
