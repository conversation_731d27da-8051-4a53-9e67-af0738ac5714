import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import tw from 'twrnc';

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const features = [
    {
      id: 'quiz-view',
      title: 'ข้อสอบพร้อมคำตอบ',
      description: 'ดูข้อสอบพร้อมคำตอบ เลือกหมวดหมู่ข้อสอบได้',
      icon: '📚',
      color: 'bg-blue-500',
      onPress: () => navigation.navigate('Category', { mode: 'view' })
    },
    {
      id: 'quiz-test',
      title: 'ทดสอบข้อสอบตามหมวดหมู่',
      description: 'เลือกทำข้อสอบตามหมวดหมู่ได้',
      icon: '✏️',
      color: 'bg-green-500',
      onPress: () => navigation.navigate('Category', { mode: 'test' })
    },
    {
      id: 'mock-exam',
      title: 'ข้อสอบเสมือนจริง',
      description: 'จำนวน 50 ข้อ, สอบผ่าน 45 ข้อ (90%)',
      icon: '🎯',
      color: 'bg-yellow-500',
      onPress: () => navigation.navigate('MockExam')
    },
    {
      id: 'admin',
      title: 'สถิติระบบ - แดชบอร์ด',
      description: 'ดูข้อมูลการใช้งานระบบแบบละเอียด',
      icon: '📊',
      color: 'bg-purple-500',
      onPress: () => navigation.navigate('Admin')
    }
  ];

  return (
    <ScrollView style={tw`flex-1 bg-gray-100`}>
      <View style={tw`p-6`}>
        {/* Header */}
        <View style={tw`mb-8 items-center bg-white rounded-xl p-6 shadow-lg`}>
          <Text style={tw`text-4xl mb-2`}>🚗</Text>
          <Text style={tw`text-3xl font-bold text-gray-800 text-center mb-2`}>
            ข้อสอบใบขับขี่
          </Text>
          <Text style={tw`text-lg text-gray-600 text-center`}>
            รถยนต์ – รถจักรยานยนต์
          </Text>
          <Text style={tw`text-sm text-blue-600 text-center mt-2`}>
            เตรียมตัวสอบใบขับขี่อย่างมั่นใจ
          </Text>
        </View>

        {/* Features */}
        <View style={tw`gap-4`}>
          {features.map((feature) => (
            <TouchableOpacity
              key={feature.id}
              style={tw`${feature.color} rounded-xl p-6 shadow-lg`}
              onPress={feature.onPress}
              activeOpacity={0.8}
            >
              <View style={tw`flex-row items-center mb-3`}>
                <Text style={tw`text-4xl mr-4`}>{feature.icon}</Text>
                <Text style={tw`text-xl font-bold text-white flex-1`}>
                  {feature.title}
                </Text>
              </View>
              <Text style={tw`text-white text-base text-center opacity-90`}>
                {feature.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Footer */}
        <View style={tw`mt-8 p-4 bg-white rounded-lg shadow-sm`}>
          <Text style={tw`text-center text-gray-600 text-sm`}>
            เตรียมตัวสอบใบขับขี่อย่างมั่นใจ
          </Text>
          <Text style={tw`text-center text-gray-500 text-xs mt-1`}>
            ข้อมูลอัพเดทล่าสุด
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

export default HomeScreen;
