import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image } from 'react-native';
import tw from 'twrnc';

interface WrongAnswersScreenProps {
  navigation: any;
  route: any;
}

const WrongAnswersScreen: React.FC<WrongAnswersScreenProps> = ({ navigation, route }) => {
  const { answers, questions, categoryName, isMockExam = false } = route.params;

  // Filter wrong answers
  const wrongQuestions = questions.filter((question: any, index: number) => {
    return answers[index] !== undefined && answers[index] !== question.correctAnswer;
  });

  const getOriginalQuestionNumber = (question: any) => {
    // Extract original question number from the question
    const match = question.numbers.match(/ข้อที่ (\d+)/);
    return match ? match[1] : '?';
  };

  return (
    <View style={tw`flex-1 bg-gray-100`}>
      {/* Header */}
      {/* <View style={tw`${isMockExam ? 'bg-red-600' : 'bg-white'} p-4 shadow-sm`}>
        <Text style={tw`text-lg font-bold ${isMockExam ? 'text-white' : 'text-gray-800'} text-center`}>
          ข้อสอบที่ตอบผิด
        </Text>
        <Text style={tw`${isMockExam ? 'text-red-100' : 'text-gray-600'} text-center mt-1`}>
          {categoryName}
        </Text>
        <Text style={tw`${isMockExam ? 'text-yellow-300' : 'text-red-600'} text-center mt-1 font-medium`}>
          จำนวน {wrongQuestions.length} ข้อ
        </Text>
      </View> */}

      <ScrollView style={tw`flex-1 p-4`}>
        {wrongQuestions.length === 0 ? (
          <View style={tw`bg-white rounded-xl p-6 shadow-lg items-center`}>
            <Text style={tw`text-6xl mb-4`}>🎉</Text>
            <Text style={tw`text-xl font-bold text-green-600 text-center mb-2`}>
              ยินดีด้วย!
            </Text>
            <Text style={tw`text-gray-600 text-center`}>
              คุณตอบถูกทุกข้อ ไม่มีข้อที่ตอบผิด
            </Text>
          </View>
        ) : (
          wrongQuestions.map((question: any, index: number) => {
            const originalIndex = questions.findIndex((q: any) => q === question);
            const userAnswer = answers[originalIndex];
            
            return (
              <View key={index} style={tw`bg-white rounded-lg p-4 mb-4 shadow-md border-2 border-blue-300`}>
                {/* Question Header */}
                <View style={tw`flex-row justify-between items-center mb-3`}>
                  <Text style={tw`text-lg font-semibold ${isMockExam ? 'text-red-700' : 'text-red-600'}`}>
                    {isMockExam ?
                      `ข้อที่ ${index + 1} (จากข้อสอบข้อที่ ${getOriginalQuestionNumber(question)})` :
                      `ข้อที่ ${index + 1} (เดิมข้อที่ ${getOriginalQuestionNumber(question)})`
                    }
                  </Text>
                  <View style={tw`bg-red-100 rounded-full px-3 py-1`}>
                    <Text style={tw`text-red-600 font-medium text-sm`}>ตอบผิด</Text>
                  </View>
                </View>

                {/* Question Content */}
                <View style={tw`mb-4`}>
                  {question.question.map((item: any, qIndex: number) => (
                    <View key={qIndex} style={tw`mb-3`}>
                      {item.text && (
                        <Text style={tw`text-base text-gray-800 leading-6`}>
                          {item.text}
                        </Text>
                      )}
                      {item.image && (
                        <Image
                          source={{ uri: item.image }}
                          style={tw`w-full h-48 rounded-lg mt-2`}
                          resizeMode="contain"
                        />
                      )}
                    </View>
                  ))}
                </View>

                {/* Answer Options */}
                <View>
                  <Text style={tw`text-lg font-semibold text-gray-800 mb-3`}>
                    ตัวเลือก:
                  </Text>
                  {question.options.map((option: any, optIndex: number) => {
                    const isCorrect = optIndex === question.correctAnswer;
                    const isUserAnswer = optIndex === userAnswer;
                    
                    let bgColor = 'bg-gray-50 border-gray-200';
                    let textColor = 'text-gray-800';
                    let labelColor = 'text-gray-700';
                    
                    if (isCorrect) {
                      bgColor = 'bg-green-100 border-green-500';
                      textColor = 'text-green-800';
                      labelColor = 'text-green-700';
                    } else if (isUserAnswer) {
                      bgColor = 'bg-red-100 border-red-500';
                      textColor = 'text-red-800';
                      labelColor = 'text-red-700';
                    }

                    return (
                      <View
                        key={optIndex}
                        style={tw`mb-3 p-4 rounded-lg border-2 ${bgColor}`}
                      >
                        <View style={tw`flex-row items-center`}>
                          <Text style={tw`text-lg font-semibold mr-3 ${labelColor}`}>
                            {String.fromCharCode(65 + optIndex)}.
                          </Text>
                          <View style={tw`flex-1`}>
                            {option.text && (
                              <Text style={tw`text-base ${textColor}`}>
                                {option.text}
                              </Text>
                            )}
                            {option.image && (
                              <Image
                                source={{ uri: option.image }}
                                style={tw`w-full h-32 rounded-lg mt-2`}
                                resizeMode="contain"
                              />
                            )}
                          </View>
                          {isCorrect && (
                            <View style={tw`ml-2 bg-green-500 rounded-full px-2 py-1`}>
                              <Text style={tw`text-white text-xs font-bold`}>ถูก</Text>
                            </View>
                          )}
                          {isUserAnswer && !isCorrect && (
                            <View style={tw`ml-2 bg-red-500 rounded-full px-2 py-1`}>
                              <Text style={tw`text-white text-xs font-bold`}>คุณเลือก</Text>
                            </View>
                          )}
                        </View>
                      </View>
                    );
                  })}
                </View>
              </View>
            );
          })
        )}
      </ScrollView>

      {/* Bottom Button */}
      {/* <View style={tw`${isMockExam ? 'bg-red-600' : 'bg-white'} p-4 shadow-lg`}>
        <TouchableOpacity
          style={tw`${isMockExam ? 'bg-white' : 'bg-blue-500'} rounded-lg p-4`}
          onPress={() => navigation.goBack()}
        >
          <Text style={tw`${isMockExam ? 'text-red-600' : 'text-white'} font-bold text-lg text-center`}>
            กลับไปดูผลการสอบ
          </Text>
        </TouchableOpacity>
      </View> */}
    </View>
  );
};

export default WrongAnswersScreen;
