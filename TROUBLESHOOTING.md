# การแก้ไขปัญหา - Troubleshooting Guide

## 🚨 ปัญหาที่พบและวิธีแก้ไข

### 1. ปัญหา AsyncStorage Import Error

**ปัญหา:**
```
Unable to resolve "@react-native-async-storage/async-storage" from "src\hooks\useStatistics.ts"
```

**สาเหตุ:**
- AsyncStorage ไม่สามารถใช้งานได้บน Web platform
- การ import โดยตรงทำให้เกิด bundling error

**วิธีแก้ไข:**
1. สร้าง Cross-platform Storage Utility (`src/utils/storage.ts`)
2. ใช้ localStorage สำหรับ Web และ in-memory storage สำหรับ React Native
3. ลบ AsyncStorage package ออก: `npm uninstall @react-native-async-storage/async-storage`
4. ล้าง Metro cache: `npx expo start --clear`

### 2. ปัญหา Metro Cache

**ปัญหา:**
- แอปยังคงพยายาม import package ที่ถูกลบแล้ว
- Bundling ล้มเหลวแม้ว่าจะแก้ไขโค้ดแล้ว

**วิธีแก้ไข:**
```bash
# ล้าง Metro cache
npx expo start --clear

# หรือ
rm -rf node_modules/.cache
npm start
```

### 3. ปัญหา Firebase Connection

**ปัญหา:**
- Firebase ไม่สามารถเชื่อมต่อได้
- ข้อมูลไม่อัปเดท

**วิธีแก้ไข:**
1. ตรวจสอบ Firebase Configuration
2. ตรวจสอบ Network connection
3. ตรวจสอบ Firebase Security Rules
4. ดูสถานะการเชื่อมต่อใน FirebaseStatus component

### 4. ปัญหา Platform Compatibility

**ปัญหา:**
- โค้ดทำงานบน Web แต่ไม่ทำงานบน Mobile
- หรือในทางกลับกัน

**วิธีแก้ไข:**
- ใช้ Cross-platform utilities
- ตรวจสอบ platform-specific code
- ใช้ conditional imports

## 🛠️ วิธีการ Debug

### 1. ตรวจสอบ Console Logs
```javascript
// เปิด Browser Developer Tools
// ดู Console tab สำหรับ errors และ warnings
```

### 2. ตรวจสอบ Network Tab
```javascript
// ดู Network requests ไปยัง Firebase
// ตรวจสอบ response status
```

### 3. ตรวจสอบ Firebase Console
```javascript
// ไปที่ Firebase Console
// ดู Realtime Database
// ตรวจสอบ Security Rules
```

## 📱 การทดสอบ Cross-platform

### Web
```bash
npm start
# กด 'w' เพื่อเปิด web browser
```

### iOS Simulator
```bash
npm start
# กด 'i' เพื่อเปิด iOS simulator
```

### Android Emulator
```bash
npm start
# กด 'a' เพื่อเปิด Android emulator
```

## 🔧 คำสั่งที่มีประโยชน์

### ล้าง Cache ทั้งหมด
```bash
npx expo start --clear
rm -rf node_modules
npm install
```

### ตรวจสอบ Dependencies
```bash
npm list
npm outdated
```

### ตรวจสอบ Bundle Size
```bash
npx expo export --platform web
```

## 📋 Checklist การแก้ไขปัญหา

- [ ] ตรวจสอบ Console errors
- [ ] ล้าง Metro cache
- [ ] ตรวจสอบ package.json dependencies
- [ ] ทดสอบบน Web ก่อน
- [ ] ทดสอบบน Mobile
- [ ] ตรวจสอบ Firebase connection
- [ ] ตรวจสอบ Network requests

## 🆘 การขอความช่วยเหลือ

หากยังคงพบปัญหา:

1. **รวบรวมข้อมูล:**
   - Error messages ทั้งหมด
   - Platform ที่เกิดปัญหา
   - ขั้นตอนการทำซ้ำปัญหา

2. **ตรวจสอบ:**
   - Package versions
   - Node.js version
   - Expo CLI version

3. **ลองวิธีแก้ไขพื้นฐาน:**
   - Restart Metro bundler
   - ล้าง cache
   - ติดตั้ง dependencies ใหม่

---

**อัปเดทล่าสุด**: 2025-01-11  
**เวอร์ชัน**: 1.0.0
