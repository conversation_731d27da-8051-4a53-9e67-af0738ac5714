import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import tw from 'twrnc';
import { categories } from '../../data';

interface CategoryScreenProps {
  navigation: any;
  route: any;
}

const CategoryScreen: React.FC<CategoryScreenProps> = ({ navigation, route }) => {
  const { mode } = route.params; // 'view' or 'test'

  const handleCategoryPress = (categoryId: string, categoryName: string) => {
    if (mode === 'view') {
      navigation.navigate('QuizView', { categoryId, categoryName });
    } else if (mode === 'test') {
      navigation.navigate('QuizTest', { categoryId, categoryName });
    }
  };

  return (
    <ScrollView style={tw`flex-1 bg-gray-100`}>
      <View style={tw`p-4`}>
        {/* Header */}
        <View style={tw`mb-6 p-4 bg-white rounded-lg shadow-sm`}>
          <Text style={tw`text-xl font-bold text-gray-800 text-center mb-2`}>
            {mode === 'view' ? 'เลือกหมวดหมู่เพื่อดูข้อสอบ' : 'เลือกหมวดหมู่เพื่อทดสอบ'}
          </Text>
          <Text style={tw`text-gray-600 text-center text-sm`}>
            {mode === 'view' 
              ? 'ดูข้อสอบพร้อมคำตอบในแต่ละหมวดหมู่' 
              : 'ทำข้อสอบและตรวจคะแนนในแต่ละหมวดหมู่'
            }
          </Text>
        </View>

        {/* Categories Grid */}
        <View style={tw`gap-3`}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={tw`bg-white rounded-lg p-4 shadow-sm border border-gray-200`}
              onPress={() => handleCategoryPress(category.id, category.name)}
              activeOpacity={0.7}
            >
              <View style={tw`flex-row items-center`}>
                <Text style={tw`text-3xl mr-4`}>{category.icon}</Text>
                <View style={tw`flex-1`}>
                  <Text style={tw`text-lg font-semibold text-gray-800 mb-1`}>
                    {category.name}
                  </Text>
                  <Text style={tw`text-gray-600 text-sm mb-2`}>
                    {category.description}
                  </Text>
                  <View style={tw`flex-row justify-between items-center`}>
                    <Text style={tw`text-blue-600 font-medium text-sm`}>
                      {category.count} ข้อ
                    </Text>
                    <Text style={tw`text-gray-400 text-xs`}>
                      {mode === 'view' ? 'ดูข้อสอบ' : 'ทดสอบ'} →
                    </Text>
                  </View>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Footer */}
        <View style={tw`mt-6 p-4 bg-blue-50 rounded-lg`}>
          <Text style={tw`text-center text-blue-800 text-sm font-medium`}>
            💡 เคล็ดลับ: ควรศึกษาข้อสอบพร้อมคำตอบก่อนทำการทดสอบ
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

export default CategoryScreen;
