// Cross-platform storage utility
// Works on both React Native and Web

interface StorageInterface {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
}

class WebStorage implements StorageInterface {
  async getItem(key: string): Promise<string | null> {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('localStorage not available:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('localStorage setItem failed:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('localStorage removeItem failed:', error);
    }
  }
}

class ReactNativeStorage implements StorageInterface {
  private storage: Map<string, string>;

  constructor() {
    // Use in-memory storage as fallback for React Native
    this.storage = new Map();
    console.warn('Using in-memory storage for React Native (AsyncStorage not available)');
  }

  async getItem(key: string): Promise<string | null> {
    try {
      return this.storage.get(key) || null;
    } catch (error) {
      console.warn('In-memory storage getItem failed:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      this.storage.set(key, value);
    } catch (error) {
      console.warn('In-memory storage setItem failed:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      this.storage.delete(key);
    } catch (error) {
      console.warn('In-memory storage removeItem failed:', error);
    }
  }
}

// Detect platform and create appropriate storage instance
const createStorage = (): StorageInterface => {
  // Check if we're in a web environment
  if (typeof window !== 'undefined' && window.localStorage) {
    return new WebStorage();
  }
  
  // Check if we're in React Native environment
  if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {
    return new ReactNativeStorage();
  }
  
  // Fallback to web storage
  return new WebStorage();
};

export const storage = createStorage();
