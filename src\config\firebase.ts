import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAnalytics, isSupported } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: "AIzaSyBvSv4VAuIp9XHU2ZuQ036hl7Y4fy4Wl58",
  authDomain: "singh-2531.firebaseapp.com",
  databaseURL: "https://singh-2531-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "singh-2531",
  storageBucket: "singh-2531.firebasestorage.app",
  messagingSenderId: "916499754535",
  appId: "1:916499754535:web:355e4deeaf09fdc15a6802",
  measurementId: "G-EPB76F5MNL"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Realtime Database and get a reference to the service
export const database = getDatabase(app);

// Initialize Analytics (only if supported)
let analytics;
const initializeAnalytics = async () => {
  try {
    if (typeof window !== 'undefined' && await isSupported()) {
      analytics = getAnalytics(app);
      console.log('Firebase Analytics initialized successfully');
    } else {
      console.log('Firebase Analytics not supported in this environment');
    }
  } catch (error) {
    console.warn('Firebase Analytics initialization failed:', error);
  }
};

// Initialize analytics asynchronously
initializeAnalytics();

export { analytics };
export default app;
