import { initializeApp } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAnalytics } from 'firebase/analytics';

const firebaseConfig = {
  apiKey: "AIzaSyBvSv4VAuIp9XHU2ZuQ036hl7Y4fy4Wl58",
  authDomain: "singh-2531.firebaseapp.com",
  projectId: "singh-2531",
  storageBucket: "singh-2531.firebasestorage.app",
  messagingSenderId: "916499754535",
  appId: "1:916499754535:web:355e4deeaf09fdc15a6802",
  measurementId: "G-EPB76F5MNL"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Realtime Database and get a reference to the service
export const database = getDatabase(app);

// Initialize Analytics (optional, for web only)
let analytics;
if (typeof window !== 'undefined') {
  analytics = getAnalytics(app);
}

export { analytics };
export default app;
