{"name": "driving", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "^1.24.0", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.5", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "firebase": "^12.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "twrnc": "^4.9.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "typescript": "~5.8.3"}, "private": true}