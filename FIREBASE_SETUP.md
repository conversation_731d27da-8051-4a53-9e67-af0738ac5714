# Firebase Setup Guide

## 🔥 การตั้งค่า Firebase สำหรับแอปข้อสอบใบขับขี่

### ข้อมูล Firebase Configuration ที่ใช้

```javascript
const firebaseConfig = {
  apiKey: "AIzaSyBvSv4VAuIp9XHU2ZuQ036hl7Y4fy4Wl58",
  authDomain: "singh-2531.firebaseapp.com",
  projectId: "singh-2531",
  storageBucket: "singh-2531.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:355e4deeaf09fdc15a6802",
  measurementId: "G-EPB76F5MNL"
};
```

### 📊 โครงสร้าง Database

Firebase Realtime Database จะมีโครงสร้างดังนี้:

```
singh-2531/
├── statistics/
│   ├── totalPageViews: number
│   ├── totalTestTakers: number
│   ├── totalStudyViews: number
│   ├── mockExamTestTakers: number
│   ├── pageViews: Array<{page: string, views: number}>
│   ├── categoryStats: Array<{name: string, testTakers: number, studyViews: number}>
│   ├── mockExamLeaderboard: Array<MockExamResult>
│   ├── mostPopularPage: string
│   └── mostPopularCategory: string
├── pageViews/
│   └── [timestamp]: {page: string, timestamp: number}
├── testTakers/
│   └── [timestamp]: {category: string, mode: string, timestamp: number}
└── mockExamResults/
    └── [resultId]: {userName: string, userEmail: string, score: number, duration: number, timestamp: number}
```

### 🚀 การตั้งค่าเริ่มต้น

1. **เข้าสู่ Firebase Console**
   - ไปที่ https://console.firebase.google.com/
   - เลือกโปรเจค `singh-2531`

2. **ตั้งค่า Realtime Database**
   - ไปที่ Realtime Database
   - เลือก "Create Database"
   - เลือก region ที่เหมาะสม
   - เริ่มต้นด้วย test mode

3. **นำเข้าข้อมูลตัวอย่าง**
   - ใช้ไฟล์ `firebase-database-structure.json`
   - Import ข้อมูลเข้าสู่ Database

### 🔒 Security Rules

ตั้งค่า Security Rules สำหรับ Realtime Database:

```json
{
  "rules": {
    "statistics": {
      ".read": true,
      ".write": true
    },
    "pageViews": {
      ".read": true,
      ".write": true
    },
    "testTakers": {
      ".read": true,
      ".write": true
    },
    "mockExamResults": {
      ".read": true,
      ".write": true
    }
  }
}
```

### 📱 การใช้งานในแอป

แอปจะใช้ Firebase สำหรับ:

1. **เก็บสถิติการใช้งาน**
   - การเข้าชมหน้าต่างๆ
   - จำนวนผู้ทำข้อสอบ
   - สถิติตามหมวดหมู่

2. **บันทึกผลการสอบ**
   - ผลคะแนนข้อสอบจำลอง
   - เวลาที่ใช้ในการทำข้อสอบ
   - ข้อมูลผู้ทำข้อสอบ

3. **แสดงอันดับ (Leaderboard)**
   - Top 10 คะแนนสูงสุด
   - เรียงตามคะแนนและเวลา

### 🔄 การทำงานแบบ Offline

แอปมีระบบ fallback ด้วย AsyncStorage:
- หาก Firebase ไม่พร้อมใช้งาน จะใช้ข้อมูลจาก AsyncStorage
- ข้อมูลจะถูก sync กลับไปยัง Firebase เมื่อเชื่อมต่อได้

### 📊 การติดตาม Analytics

Firebase Analytics จะติดตาม:
- การใช้งานแอป
- หน้าที่ได้รับความนิยม
- พฤติกรรมผู้ใช้

### 🛠️ การ Debug

ตรวจสอบการเชื่อมต่อ Firebase:
1. ดูสถานะการเชื่อมต่อในแอป (จุดสีเขียว/แดง)
2. ตรวจสอบ Console ใน Browser Developer Tools
3. ดูข้อมูลใน Firebase Console

### 📝 หมายเหตุ

- ข้อมูลจะอัปเดทแบบ real-time
- แอปรองรับการทำงานทั้ง online และ offline
- ข้อมูลสถิติจะถูกรีเซ็ตได้ผ่าน Firebase Console

### 🔧 การแก้ไขปัญหา

**ปัญหาที่พบบ่อย:**

1. **ไม่สามารถเชื่อมต่อ Firebase**
   - ตรวจสอบ API Key และ Configuration
   - ตรวจสอบ Network connection
   - ตรวจสอบ Security Rules

2. **ข้อมูลไม่อัปเดท**
   - ตรวจสอบ Database Rules
   - ตรวจสอบ Network connection
   - ลองรีเฟรชแอป

3. **Performance ช้า**
   - ตรวจสอบขนาดข้อมูลใน Database
   - ใช้ Indexing ที่เหมาะสม
   - จำกัดจำนวนข้อมูลที่ query

---

**อัปเดทล่าสุด**: 2025-01-11  
**เวอร์ชัน**: 1.0.0
