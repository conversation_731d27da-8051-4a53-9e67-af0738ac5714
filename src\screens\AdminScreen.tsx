import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, RefreshControl } from 'react-native';
import { useStatistics } from '../hooks/useStatistics';
import { formatNumber } from '../utils/numberFormatter';
import FirebaseStatus from '../components/FirebaseStatus';
import tw from 'twrnc';

interface AdminScreenProps {
  navigation: any;
}

const AdminScreen: React.FC<AdminScreenProps> = ({ navigation }) => {
  const { statistics, isLoading, refreshStatistics } = useStatistics();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshStatistics();
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  if (isLoading) {
    return (
      <View style={tw`flex-1 items-center justify-center bg-gray-100`}>
        <View style={tw`items-center`}>
          <View style={tw`w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4`} />
          <Text style={tw`text-gray-600 text-lg`}>กำลังโหลดสถิติ...</Text>
        </View>
      </View>
    );
  }

  return (
    <ScrollView 
      style={tw`flex-1 bg-gray-100`}
      refreshControl={
        <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={tw`p-6`}>
        {/* Header */}
        <View style={tw`mb-8`}>
          <View style={tw`flex-row justify-between items-start mb-4`}>
            <View style={tw`flex-1`}>
              <Text style={tw`text-3xl font-bold text-gray-800 mb-2`}>
                📊 สถิติระบบ - แดชบอร์ด
              </Text>
              <Text style={tw`text-gray-600 mb-3 text-base`}>
                ข้อมูลการใช้งานระบบข้อสอบใบขับขี่แบบละเอียด
              </Text>
              <FirebaseStatus />
            </View>
            <TouchableOpacity
              onPress={() => navigation.goBack()}
              style={tw`bg-gray-600 px-4 py-2 rounded-lg ml-4`}
            >
              <Text style={tw`text-white font-medium`}>🏠 กลับหน้าหลัก</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Overview Cards */}
        <View style={tw`mb-8`}>
          <View style={tw`flex-row flex-wrap -mx-2`}>
            <View style={tw`w-1/2 px-2 mb-4`}>
              <View style={tw`bg-white rounded-xl shadow-lg p-4 border-l-4 border-blue-500`}>
                <View style={tw`flex-row items-center justify-between`}>
                  <View style={tw`flex-1`}>
                    <Text style={tw`text-sm font-medium text-gray-600`}>การเข้าชมทั้งหมด</Text>
                    <Text style={tw`text-xl font-bold text-gray-900`}>
                      {formatNumber(statistics.totalPageViews)}
                    </Text>
                  </View>
                  <Text style={tw`text-2xl text-blue-500`}>📈</Text>
                </View>
              </View>
            </View>

            <View style={tw`w-1/2 px-2 mb-4`}>
              <View style={tw`bg-white rounded-xl shadow-lg p-4 border-l-4 border-green-500`}>
                <View style={tw`flex-row items-center justify-between`}>
                  <View style={tw`flex-1`}>
                    <Text style={tw`text-sm font-medium text-gray-600`}>ผู้ทำข้อสอบ</Text>
                    <Text style={tw`text-xl font-bold text-gray-900`}>
                      {formatNumber(statistics.totalTestTakers)}
                    </Text>
                  </View>
                  <Text style={tw`text-2xl text-green-500`}>👥</Text>
                </View>
              </View>
            </View>

            <View style={tw`w-1/2 px-2 mb-4`}>
              <View style={tw`bg-white rounded-xl shadow-lg p-4 border-l-4 border-purple-500`}>
                <View style={tw`flex-row items-center justify-between`}>
                  <View style={tw`flex-1`}>
                    <Text style={tw`text-sm font-medium text-gray-600`}>การศึกษา</Text>
                    <Text style={tw`text-xl font-bold text-gray-900`}>
                      {formatNumber(statistics.totalStudyViews)}
                    </Text>
                  </View>
                  <Text style={tw`text-2xl text-purple-500`}>📚</Text>
                </View>
              </View>
            </View>

            <View style={tw`w-1/2 px-2 mb-4`}>
              <View style={tw`bg-white rounded-xl shadow-lg p-4 border-l-4 border-orange-500`}>
                <View style={tw`flex-row items-center justify-between`}>
                  <View style={tw`flex-1`}>
                    <Text style={tw`text-sm font-medium text-gray-600`}>ข้อสอบจำลอง</Text>
                    <Text style={tw`text-xl font-bold text-gray-900`}>
                      {formatNumber(statistics.mockExamTestTakers)}
                    </Text>
                  </View>
                  <Text style={tw`text-2xl text-orange-500`}>🗄️</Text>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Detailed Statistics */}
        <View style={tw`mb-8`}>
          {/* Popular Pages */}
          <View style={tw`bg-white rounded-xl shadow-lg p-6 mb-6`}>
            <Text style={tw`text-xl font-bold text-gray-800 mb-4`}>
              📄 หน้าที่ได้รับความนิยม
            </Text>
            <View style={tw`gap-3`}>
              {statistics.pageViews.slice(0, 5).map((page, index) => (
                <View key={page.page} style={tw`flex-row items-center justify-between p-3 bg-gray-50 rounded-lg`}>
                  <View style={tw`flex-row items-center gap-3 flex-1`}>
                    <View style={tw`w-6 h-6 bg-blue-100 rounded-full items-center justify-center`}>
                      <Text style={tw`text-blue-600 text-sm font-bold`}>{index + 1}</Text>
                    </View>
                    <Text style={tw`font-medium flex-1`} numberOfLines={1}>{page.page}</Text>
                  </View>
                  <Text style={tw`text-blue-600 font-bold`}>{formatNumber(page.views)} ครั้ง</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Category Statistics */}
          <View style={tw`bg-white rounded-xl shadow-lg p-6 mb-6`}>
            <Text style={tw`text-xl font-bold text-gray-800 mb-4`}>
              📚 สถิติตามหมวดหมู่
            </Text>
            <View style={tw`gap-3`}>
              {statistics.categoryStats.slice(0, 5).map((category, index) => (
                <View key={category.name} style={tw`p-3 bg-gray-50 rounded-lg`}>
                  <View style={tw`flex-row items-center justify-between mb-2`}>
                    <Text style={tw`font-medium flex-1`} numberOfLines={1}>{category.name}</Text>
                    <Text style={tw`text-sm text-gray-500`}>#{index + 1}</Text>
                  </View>
                  <View style={tw`flex-row justify-between`}>
                    <Text style={tw`text-sm text-green-600`}>
                      ทำข้อสอบ: {formatNumber(category.testTakers)}
                    </Text>
                    <Text style={tw`text-sm text-purple-600`}>
                      ศึกษา: {formatNumber(category.studyViews)}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Mock Exam Leaderboard */}
        <View style={tw`bg-white rounded-xl shadow-lg p-6 mb-8`}>
          <Text style={tw`text-xl font-bold text-gray-800 mb-4`}>
            🏆 อันดับข้อสอบจำลอง (Top 10)
          </Text>
          {statistics.mockExamLeaderboard.length > 0 ? (
            <View style={tw`gap-3`}>
              {statistics.mockExamLeaderboard.map((result, index) => (
                <View key={result.id || index} style={tw`p-4 bg-gray-50 rounded-lg`}>
                  <View style={tw`flex-row items-center justify-between mb-2`}>
                    <View style={tw`flex-row items-center gap-3 flex-1`}>
                      <View style={tw`w-8 h-8 rounded-full items-center justify-center ${
                        index === 0 ? 'bg-yellow-500' :
                        index === 1 ? 'bg-gray-400' :
                        index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                      }`}>
                        <Text style={tw`text-white font-bold text-sm`}>{index + 1}</Text>
                      </View>
                      <View style={tw`flex-1`}>
                        <Text style={tw`font-medium`} numberOfLines={1}>{result.userName}</Text>
                        <Text style={tw`text-sm text-gray-600`} numberOfLines={1}>{result.userEmail}</Text>
                      </View>
                    </View>
                    <View style={tw`items-end`}>
                      <View style={tw`bg-green-100 px-2 py-1 rounded-full mb-1`}>
                        <Text style={tw`text-green-800 text-xs font-medium`}>
                          {result.score}/50
                        </Text>
                      </View>
                      <Text style={tw`text-xs text-gray-600`}>
                        {Math.floor(result.duration / 60)}:{(result.duration % 60).toString().padStart(2, '0')}
                      </Text>
                    </View>
                  </View>
                  <Text style={tw`text-xs text-gray-500`}>
                    {new Date(result.timestamp).toLocaleDateString('th-TH')}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <View style={tw`py-8 items-center`}>
              <Text style={tw`text-gray-500 text-center`}>ยังไม่มีข้อมูลการทำข้อสอบจำลอง</Text>
            </View>
          )}
        </View>

        {/* Summary Info */}
        <View style={tw`bg-blue-50 border border-blue-200 rounded-xl p-6`}>
          <Text style={tw`text-lg font-bold text-blue-800 mb-2`}>
            📋 สรุปข้อมูล
          </Text>
          <View style={tw`gap-2`}>
            <Text style={tw`text-sm text-blue-700`}>
              <Text style={tw`font-bold`}>หน้าที่ได้รับความนิยมมากที่สุด:</Text> {statistics.mostPopularPage}
            </Text>
            <Text style={tw`text-sm text-blue-700`}>
              <Text style={tw`font-bold`}>หมวดหมู่ที่ได้รับความนิยมมากที่สุด:</Text> {statistics.mostPopularCategory}
            </Text>
            <Text style={tw`text-sm text-blue-700`}>
              <Text style={tw`font-bold`}>ข้อมูลอัปเดตล่าสุด:</Text> {new Date().toLocaleString('th-TH')}
            </Text>
            <Text style={tw`text-sm text-blue-700`}>
              <Text style={tw`font-bold`}>แหล่งข้อมูล:</Text> Firebase Database + AsyncStorage (สำรอง)
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default AdminScreen;
