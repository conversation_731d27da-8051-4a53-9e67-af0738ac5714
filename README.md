# ข้อสอบใบขับขี่รถยนต์ – รถจักรยานยนต์

แอปพลิเคชันสำหรับเตรียมตัวสอบใบขับขี่รถยนต์และรถจักรยานยนต์ พัฒนาด้วย React Native และ Expo

## ✨ Features

### 1. 📚 ข้อสอบพร้อมคำตอบ
- ดูข้อสอบพร้อมคำตอบที่ถูกต้อง
- เลือกหมวดหมู่ข้อสอบได้
- เลื่อนดูข้อสอบทีละข้อ
- แสดงคำตอบที่ถูกต้องพร้อมเหตุผล

### 2. ✏️ ทดสอบข้อสอบตามหมวดหมู่
- เลือกทำข้อสอบตามหมวดหมู่ที่ต้องการ
- ระบบจับเวลาการทำข้อสอบ
- ตรวจคำตอบและแสดงผลคะแนน
- เกณฑ์ผ่าน 90%

### 3. 🎯 ข้อสอบเสมือนจริง
- ข้อสอบ 50 ข้อ สุ่มจากทุกหมวดหมู่ (ไฟล์ละ 10 ข้อ)
- จำกัดเวลา 50 นาที
- เกณฑ์ผ่าน 45 ข้อ (90%)
- จำลองสถานการณ์การสอบจริง

### 4. 📊 สถิติระบบ - แดชบอร์ด
- แสดงสถิติการใช้งานระบบแบบละเอียด
- ติดตามการเข้าชมหน้าต่างๆ
- สถิติผู้ทำข้อสอบแยกตามหมวดหมู่
- อันดับข้อสอบจำลอง (Leaderboard)
- เชื่อมต่อกับ Firebase Realtime Database

## 📋 หมวดหมู่ข้อสอบ

1. **การคาดการณ์อุบัติเหตุ** 🚑
2. **การบำรุงรักษารถ** 🔧
3. **การขับรถอย่างปลอดภัย** 🛡️
4. **กฎหมายจราจร** 📋
5. **อุปกรณ์ควบคุมจราจร** 🚦

## 🚀 การติดตั้งและรัน

### ข้อกำหนดเบื้องต้น
- Node.js (version 18 หรือสูงกว่า)
- npm หรือ yarn
- Expo CLI

### การติดตั้ง

1. Clone repository
```bash
git clone <repository-url>
cd driving
```

2. ติดตั้ง dependencies
```bash
npm install
```

3. รัน app
```bash
npm start
```

### การรันบนแพลตฟอร์มต่างๆ

- **Web**: `npm run web` หรือกด `w` ใน Expo CLI
- **iOS**: `npm run ios` หรือกด `i` ใน Expo CLI
- **Android**: `npm run android` หรือกด `a` ใน Expo CLI

## 🛠️ เทคโนโลยีที่ใช้

- **React Native** - Framework สำหรับพัฒนา mobile app
- **Expo** - Platform สำหรับพัฒนา React Native app
- **TypeScript** - Type-safe JavaScript
- **React Navigation** - Navigation library
- **Tailwind React Native Classnames (twrnc)** - Utility-first CSS framework
- **React Native Web** - รองรับการรันบน web browser
- **Firebase** - Backend และ Realtime Database
- **Cross-platform Storage** - Local storage ที่รองรับทั้ง Web และ Mobile

## 📱 การใช้งาน

### หน้าหลัก
- เลือกโหมดการใช้งาน: ดูข้อสอบ, ทดสอบ, หรือสอบเสมือนจริง

### การดูข้อสอบพร้อมคำตอบ
1. เลือก "ข้อสอบพร้อมคำตอบ"
2. เลือกหมวดหมู่ที่ต้องการ
3. ดูข้อสอบและคำตอบที่ถูกต้อง (แสดงเป็นสีเขียว)

### การทดสอบข้อสอบ
1. เลือก "ทดสอบข้อสอบตามหมวดหมู่"
2. เลือกหมวดหมู่ที่ต้องการทดสอบ
3. ตอบข้อสอบภายในเวลาที่กำหนด
4. ดูผลคะแนนและการวิเคราะห์

### ข้อสอบเสมือนจริง
1. เลือก "ข้อสอบเสมือนจริง"
2. ทำข้อสอบ 50 ข้อ ภายใน 50 นาที (สุ่มไฟล์ละ 10 ข้อ)
3. ต้องได้คะแนน 45 ข้อขึ้นไป (90%) เพื่อผ่าน

### สถิติระบบ - แดชบอร์ด
1. เลือก "สถิติระบบ - แดชบอร์ด"
2. ดูสถิติการใช้งานระบบแบบละเอียด
3. ติดตามการเข้าชมหน้าต่างๆ และสถิติผู้ทำข้อสอบ
4. ดูอันดับข้อสอบจำลอง (Top 10)
5. ข้อมูลจะอัปเดทแบบ real-time จาก Firebase

## 📊 ระบบการให้คะแนน

- **A**: 90-100% (ยอดเยี่ยม)
- **B**: 80-89% (ดีมาก)
- **C**: 70-79% (ดี)
- **D**: 60-69% (พอใช้)
- **F**: ต่ำกว่า 60% (ไม่ผ่าน)

## 🎨 UI/UX Design

- ออกแบบให้ใช้งานง่าย เหมาะสำหรับทุกวัย
- รองรับทั้ง mobile และ web
- ใช้สีสันที่เหมาะสมและไม่ทำให้ตาเมื่อย
- แสดงผลรูปภาพข้อสอบได้ชัดเจน

## 📝 หมายเหตุ

- ข้อมูลข้อสอบอัพเดทตามกฎหมายล่าสุด
- แนะนำให้ศึกษาข้อสอบพร้อมคำตอบก่อนทำการทดสอบ
- ควรทำข้อสอบเสมือนจริงหลายครั้งเพื่อเตรียมความพร้อม

## 🤝 การสนับสนุน

หากพบปัญหาหรือต้องการข้อมูลเพิ่มเติม กรุณาติดต่อผู้พัฒนา

---

**พัฒนาโดย**: Augment Agent  
**เวอร์ชัน**: 1.0.0  
**อัพเดทล่าสุด**: 2025
