import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Image, Alert, TextInput } from 'react-native';
import tw from 'twrnc';
import { generateMockExam, Question, mockExamConfig } from '../../data';

interface MockExamScreenProps {
  navigation: any;
  route: any;
}

const MockExamScreen: React.FC<MockExamScreenProps> = ({ navigation, route }) => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: number }>({});
  const [timeLeft, setTimeLeft] = useState(0); // will start after pressing start
  const [isFinished, setIsFinished] = useState(false);
  const [inputValue, setInputValue] = useState('1');
  const [showForm, setShowForm] = useState(true);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [showAllQuestions, setShowAllQuestions] = useState(false);

  useEffect(() => {
    // Prepare questions according to configured counts
    const mockQuestions = generateMockExam();
    setQuestions(mockQuestions);
  }, []);

  // Reset exam when resetExam parameter is passed
  useEffect(() => {
    if (route?.params?.resetExam) {
      // Reset all states to initial values
      setCurrentIndex(0);
      setAnswers({});
      setTimeLeft(0);
      setIsFinished(false);
      setInputValue('1');
      setShowForm(true);
      setUserName('');
      setUserEmail('');
      setShowAllQuestions(false);

      // Generate new questions
      const mockQuestions = generateMockExam();
      setQuestions(mockQuestions);
    }
  }, [route?.params?.resetExam]);

  // Start timer only after pressing start (form hidden)
  useEffect(() => {
    if (timeLeft > 0 && !isFinished && !showForm) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && !isFinished && !showForm && questions.length > 0) {
      handleFinishExam();
    }
  }, [timeLeft, isFinished, showForm, questions.length]);

  useEffect(() => {
    setInputValue((currentIndex + 1).toString());
  }, [currentIndex]);

  // Navigation functions
  const handleFirst = () => {
    setCurrentIndex(0);
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const handleLast = () => {
    setCurrentIndex(questions.length - 1);
  };

  const handleInputChange = (text: string) => {
    setInputValue(text);
  };

  const handleKeyDown = () => {
    const questionNumber = parseInt(inputValue);
    if (questionNumber >= 1 && questionNumber <= questions.length) {
      setCurrentIndex(questionNumber - 1);
    } else {
      Alert.alert(
        'หมายเลขข้อไม่ถูกต้อง',
        `กรุณาใส่หมายเลขข้อระหว่าง 1 ถึง ${questions.length}`,
        [{ text: 'ตกลง' }]
      );
      setInputValue((currentIndex + 1).toString());
    }
  };

  const startExam = () => {
    if (!userName.trim() || !userEmail.trim()) {
      Alert.alert('กรุณากรอกข้อมูล', 'กรุณากรอกชื่อและอีเมลให้ครบถ้วน');
      return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userEmail)) {
      Alert.alert('อีเมลไม่ถูกต้อง', 'กรุณากรอกอีเมลในรูปแบบที่ถูกต้อง');
      return;
    }
    setShowForm(false);
    setTimeLeft(mockExamConfig.timeLimit * 60);
  };



  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Overview of all questions
  if (showAllQuestions && !showForm) {
    return (
      <View style={tw`flex-1 bg-gray-100`}>
        <View style={tw`bg-white p-4 shadow-sm`}>
          <View style={tw`flex-row justify-between items-center`}>
            <Text style={tw`text-lg font-bold text-gray-800`}>
              ภาพรวมข้อสอบ (50 ข้อ)
            </Text>
            <TouchableOpacity
              style={tw`bg-red-500 rounded-lg px-4 py-2`}
              onPress={() => setShowAllQuestions(false)}
            >
              <Text style={tw`text-white font-medium`}>ปิด</Text>
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView style={tw`flex-1 p-4`}>
          <View style={tw`bg-white rounded-lg p-4 shadow-sm`}>
            <Text style={tw`text-lg font-semibold text-gray-800 mb-4`}>
              สถานะการทำข้อสอบ ({Object.keys(answers).length}/{questions.length})
            </Text>

            <View style={tw`flex-row flex-wrap gap-2`}>
              {questions.map((_, index) => (
                <TouchableOpacity
                  key={index}
                  style={tw`w-12 h-12 rounded-lg border-2 items-center justify-center ${
                    answers[index] !== undefined
                      ? 'bg-green-100 border-green-500'
                      : 'bg-gray-100 border-gray-300'
                  } ${currentIndex === index ? 'border-blue-500 bg-blue-100' : ''}`}
                  onPress={() => {
                    setCurrentIndex(index);
                    setShowAllQuestions(false);
                  }}
                >
                  <Text style={tw`font-bold ${
                    answers[index] !== undefined
                      ? 'text-green-700'
                      : 'text-gray-600'
                  } ${currentIndex === index ? 'text-blue-700' : ''}`}>
                    {index + 1}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            <View style={tw`mt-6 flex-row justify-around`}>
              <View style={tw`items-center`}>
                <View style={tw`w-6 h-6 bg-green-100 border-2 border-green-500 rounded mb-1`} />
                <Text style={tw`text-sm text-gray-600`}>ทำแล้ว</Text>
              </View>
              <View style={tw`items-center`}>
                <View style={tw`w-6 h-6 bg-gray-100 border-2 border-gray-300 rounded mb-1`} />
                <Text style={tw`text-sm text-gray-600`}>ยังไม่ทำ</Text>
              </View>
              <View style={tw`items-center`}>
                <View style={tw`w-6 h-6 bg-blue-100 border-2 border-blue-500 rounded mb-1`} />
                <Text style={tw`text-sm text-gray-600`}>ข้อปัจจุบัน</Text>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  const handleAnswerSelect = (answerIndex: number) => {
    setAnswers({ ...answers, [currentIndex]: answerIndex });
  };

  const handleFinishExam = () => {
    setIsFinished(true);
    
    // Calculate score
    let correctCount = 0;
    questions.forEach((question, index) => {
      if (answers[index] === question.correctAnswer) {
        correctCount++;
      }
    });

    const score = correctCount;
    const totalQuestions = questions.length;
    const percentage = Math.round((score / totalQuestions) * 100);
    const passed = score >= 45; // 90% of 50 questions = 45

    navigation.navigate('Result', {
      score,
      totalQuestions,
      percentage,
      passed,
      categoryName: 'ข้อสอบเสมือนจริง',
      answers,
      questions,
      isMockExam: true,
      userName,
      userEmail
    });
  };

  const confirmFinish = () => {
    Alert.alert(
      'ยืนยันการส่งข้อสอบ',
      'คุณต้องการส่งข้อสอบหรือไม่?',
      [
        { text: 'ยกเลิก', style: 'cancel' },
        { text: 'ส่งข้อสอบ', onPress: handleFinishExam }
      ]
    );
  };

  if (questions.length === 0) {
    return (
      <View style={tw`flex-1 bg-gray-100 justify-center items-center p-6`}>
        <Text style={tw`text-3xl mb-4`}>🎯</Text>
        <Text style={tw`text-gray-600 text-lg text-center`}>กำลังสร้างข้อสอบเสมือนจริง...</Text>
        <Text style={tw`text-gray-500 text-sm text-center mt-2`}>สุ่มข้อสอบ 50 ข้อ ตามสัดส่วนที่กำหนด</Text>
      </View>
    );
  }

  // Show form before starting
  if (showForm) {
    return (
      <View style={tw`flex-1 bg-gray-100`}>
        <ScrollView style={tw`flex-1 p-6`}>
          <View style={tw`bg-white rounded-xl p-6 shadow-lg`}>
            <Text style={tw`text-2xl font-bold text-gray-800 text-center mb-6`}>
              เริ่มข้อสอบเสมือนจริง (50 ข้อ)
            </Text>

            <View style={tw`my-4`}>
              <Text style={tw`text-gray-700 font-medium mb-2`}>ชื่อ-นามสกุล *</Text>
              <TextInput
                style={tw`border-2 border-gray-300 rounded-lg px-2 pb-4 pt-2 text-base`}
                placeholder="กรุณากรอกชื่อ-นามสกุล"
                value={userName}
                onChangeText={setUserName}
              />
            </View>

            <View style={tw`mb-6`}>
              <Text style={tw`text-gray-700 font-medium mb-2`}>อีเมล *</Text>
              <TextInput
                style={tw`border-2 border-gray-300 rounded-lg px-2 pb-4 pt-2 text-base`}
                placeholder="กรุณากรอกอีเมล"
                value={userEmail}
                onChangeText={setUserEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={tw`bg-blue-50 rounded-lg p-4 my-6`}>
              <Text style={tw`text-blue-800 text-lg font-medium mb-2`}>รายละเอียดการสอบ</Text>
              <Text style={tw`text-blue-700 mb-1`}>• จำนวนข้อสอบ: 50 ข้อ</Text>
              <Text style={tw`text-blue-700 mb-1`}>• เวลาในการทำ: {mockExamConfig.timeLimit} นาที</Text>
              <Text style={tw`text-blue-700 mb-1`}>• เกณฑ์ผ่าน: 90% (45 ข้อ)</Text>
              <Text style={tw`text-blue-700`}>• ข้อสอบจะถูกสุ่มจากแต่ละหมวดและสลับลำดับ</Text>
            </View>

            <TouchableOpacity
              style={tw`bg-green-500 rounded-lg p-4 my-4`}
              onPress={startExam}
            >
              <Text style={tw`text-white font-bold text-lg text-center`}>
                เริ่มทำข้อสอบ
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    );
  }

  const currentQuestion = questions[currentIndex];
  const selectedAnswer = answers[currentIndex];

  return (
    <View style={tw`flex-1 bg-gray-100`}>
      {/* Header */}
      <View style={tw`bg-blue-600 p-4 shadow-sm`}>
        <View style={tw`flex-row justify-between items-center mb-2`}>
          <Text style={tw`text-lg font-bold text-white`}>
            ข้อสอบเสมือนจริง
          </Text>
          <Text style={tw`text-lg font-bold ${timeLeft < 600 ? 'text-yellow-300' : 'text-white'}`}>
            ⏰ {formatTime(timeLeft)}
          </Text>
        </View>
        <View style={tw`flex-row justify-between items-center mb-2`}>
          <Text style={tw`text-red-100`}>
            ข้อ {currentIndex + 1} จาก {questions.length}
          </Text>
          <Text style={tw`text-red-100`}>
            ตอบแล้ว: {Object.keys(answers).length}/{questions.length}
          </Text>
        </View>
        <TouchableOpacity
          style={tw`bg-white/20 rounded-lg p-2 mt-1`}
          onPress={() => setShowAllQuestions(true)}
        >
          <Text style={tw`text-white font-medium text-center`}>
            📋 ดูข้อสอบทั้งหมด
          </Text>
        </TouchableOpacity>
        {/* <View style={tw`mt-2 bg-red-700 rounded-full h-2`}>
          <View
            style={[
              tw`bg-white rounded-full h-2`,
              { width: `${((currentIndex + 1) / questions.length) * 100}%` }
            ]}
          />
        </View> */}
      </View>

      <ScrollView style={tw`flex-1 p-4`}>
        {/* Question */}
        <View style={tw`bg-white rounded-lg p-4 mb-4 shadow-sm`}>
          <Text style={tw`text-lg font-semibold text-blue-800 mb-3`}>
            {currentQuestion.numbers}
          </Text>
          
          {currentQuestion.question.map((item, index) => (
            <View key={index} style={tw`mb-3`}>
              {item.text && (
                <Text style={tw`text-base text-gray-800 leading-6`}>
                  {item.text}
                </Text>
              )}
              {item.image && (
                <Image
                  source={{ uri: item.image }}
                  style={tw`w-full h-60 rounded-lg mt-2`}
                  resizeMode="contain"
                />
              )}
            </View>
          ))}
        </View>

        {/* Options */}
        <View style={tw`bg-white rounded-lg p-4 shadow-sm`}>
          <Text style={tw`text-lg font-semibold text-gray-800 mb-4`}>
            เลือกคำตอบ:
          </Text>
          {currentQuestion.options.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={tw`mb-3 p-3 rounded-lg border-2 ${
                selectedAnswer === index 
                  ? 'bg-blue-50 border-blue-500' 
                  : 'bg-gray-50 border-gray-200'
              }`}
              onPress={() => handleAnswerSelect(index)}
            >
              <View style={tw`flex-row items-start`}>
                <Text style={tw`mr-3 flex-shrink-0 w-6 h-6 rounded-full border-2 text-center text-sm font-semibold mt-1 ${
                  selectedAnswer === index ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-400 text-gray-400'
                }`}>
                  {String.fromCharCode(65 + index)}
                </Text>
                <View style={tw`flex-1`}>
                  {option.text && (
                    <Text style={tw`text-base ${
                      selectedAnswer === index ? 'text-blue-800' : 'text-gray-800'
                    }`}>
                      {option.text}
                    </Text>
                  )}
                  {option.image && (
                    <Image
                      source={{ uri: option.image }}
                      style={tw`w-full h-32 rounded-lg mt-2`}
                      resizeMode="contain"
                    />
                  )}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
        </ScrollView>

      {/* Enhanced Navigation */}
      <View style={tw`bg-gray-100 p-4 shadow-lg`}>
        {/* Navigation Controls */}
        <View style={tw`bg-white rounded-lg p-3 shadow-sm mb-3`}>
          <View style={tw`flex-row items-center justify-center gap-5`}>
            {/* First Button */}
            <TouchableOpacity
              style={tw`py-2 px-3 rounded-md ${
                currentIndex === 0 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleFirst}
              disabled={currentIndex === 0}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === 0 ? 'text-gray-500' : 'text-white'
              }`}>
                ‹‹
              </Text>
            </TouchableOpacity>

            {/* Previous Button */}
            <TouchableOpacity
              style={tw`py-2 px-4 rounded-md ${
                currentIndex === 0 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handlePrevious}
              disabled={currentIndex === 0}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === 0 ? 'text-gray-500' : 'text-white'
              }`}>
                ‹
              </Text>
            </TouchableOpacity>

            {/* Question Number Input */}
            <View style={tw`flex-row items-center mx-2`}>
              <TextInput
                style={tw`p-3 text-center border-2 border-blue-500 w-20 rounded-md bg-white`}
                value={inputValue}
                onChangeText={handleInputChange}
                onSubmitEditing={handleKeyDown}
                selectTextOnFocus={true}
                keyboardType="numeric"
                returnKeyType="go"
              />
            </View>

            {/* Next Button */}
            <TouchableOpacity
              style={tw`py-2 px-4 rounded-md ${
                currentIndex === questions.length - 1 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleNext}
              disabled={currentIndex === questions.length - 1}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === questions.length - 1 ? 'text-gray-500' : 'text-white'
              }`}>
                ›
              </Text>
            </TouchableOpacity>

            {/* Last Button */}
            <TouchableOpacity
              style={tw`py-2 px-3 rounded-md ${
                currentIndex === questions.length - 1 ? 'bg-gray-300' : 'bg-blue-500'
              }`}
              onPress={handleLast}
              disabled={currentIndex === questions.length - 1}
            >
              <Text style={tw`text-xl font-bold ${
                currentIndex === questions.length - 1 ? 'text-gray-500' : 'text-white'
              }`}>
                ››
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Submit Button - only show when all answered */}
        {Object.keys(answers).length === questions.length && (
          <TouchableOpacity
            style={tw`py-3 px-4 rounded-lg bg-green-500`}
            onPress={confirmFinish}
          >
            <Text style={tw`text-center font-bold text-white text-lg`}>
              ส่งข้อสอบ
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default MockExamScreen;
